<template>
  <div class="create-plan-wrapper">
    <span class="header">创建任务</span>
    <div class="content" style="position: relative;">
      <a-form ref="valueRef" :layout="formState.layout" :hideRequiredMark="true" :rules="rules" :model="planBody"
        labelAlign="left">
        <a-form-item label="任务名称" name="name" :labelCol="{ span: 23 }">
          <a-input style="background-color: #232323;" placeholder="请输入计划名称" v-model:value="planBody.name" />
        </a-form-item>

        <!-- 任务类型 -->
        <a-form-item label="任务类型" :labelCol="{ span: 23 }">
          <div style="white-space: nowrap;">
            <a-radio-group v-model:value="planBody.mission_type" button-style="solid" size="small">
              <a-radio-button value="normal">普通任务</a-radio-button>
              <a-radio-button value="leapfrog">蛙跳任务</a-radio-button>
            </a-radio-group>
          </div>
        </a-form-item>

        <!-- 航线 -->
        <a-form-item label="执行航线" name="wayline_uuid">
          <a-input style="background-color: #232323;" placeholder="请选择航线" v-model:value="fileName" @click="openDialog"
            readonly />
        </a-form-item>

        <a-form-item v-if="planBody.mission_type === 'leapfrog'" label="执行设备（起飞）" name="landing_dock_sn"
          :labelCol="{ span: 23 }">
          <a-select placeholder="选择执行设备（起飞）" v-model:value="planBody.landing_dock_sn"
            style="width: 100%;background: black;">
            <a-select-option :value="dock.device_sn" v-for="dock in docksData.data">
              {{ dock.nickname }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 设备 -->
        <a-form-item :label="planBody.mission_type === 'leapfrog' ? '执行设备（降落）' : '选择设备'" name="sn">
          <a-select :placeholder="planBody.mission_type === 'leapfrog' ? '选择执行设备（降落）' : '选择设备'" @change="dockChange"
            v-model:value="planBody.sn" style="width: 100%;background: black;">
            <a-select-option :value="dock.device_sn" v-for="dock in docksData.data">
              {{ dock.nickname }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 任务精度 -->
        <a-form-item label="任务精度" :labelCol="{ span: 23 }">
          <div style="white-space: nowrap;">
            <a-radio-group v-model:value="planBody.wayline_precision_type" button-style="solid" size="small">
              <a-radio-button :value="1">高精度 RTK</a-radio-button>
              <a-radio-button :value="0" :disabled="planBody.mission_type === 'leapfrog'">GNSS</a-radio-button>
            </a-radio-group>
          </div>
        </a-form-item>

        <!-- 任务策略 -->
        <a-form-item label="任务策略" class="plan-timer-form-item">
          <div style="white-space: nowrap;">
            <a-radio-group v-model:value="planBody.task_type" button-style="solid" size="small">
              <a-radio-button v-for="item in TaskStrategyOptions" :value="item.value" :key="item.value">
                {{ item.label }}
              </a-radio-button>
            </a-radio-group>
          </div>
        </a-form-item>
        <a-form-item label="执行日期" v-if="planBody.task_type === 3" name="select_execute_date" :labelCol="{ span: 23 }">
          <a-date-picker v-model:value="planBody.select_execute_date" format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledDate" :show-time="{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }" />
        </a-form-item>

        <!-- 执行日期 -->
        <a-form-item label="执行日期" v-if="[2, 5].includes(planBody.task_type)" name="select_execute_date_range"
          :labelCol="{ span: 23 }">
          <a-range-picker v-model:value="planBody.select_execute_date_range"
            :disabledDate="(current: Dayjs) => current < dayjs().subtract(1, 'days')" format="YYYY-MM-DD"
            :placeholder="['开始时间', '结束时间']" style="width: 100%;" />
        </a-form-item>

        <!-- 执行时间 -->
        <a-form-item v-if="[2, 5].includes(planBody.task_type)" label="执行时间" name="select_execute_time"
          ref="select_execute_time" :labelCol="{ span: 23 }" :autoLink="false">
          <div class="time-picker-wrapper" v-for="n in planBody.select_time_number" :key="n">
            <a-time-picker v-model:value="planBody.select_time[n - 1][0]" format="HH:mm:ss" show-time placeholder="开始时间"
              :style="planBody.task_type === 5 ? 'width: 40%' : 'width: 82%'"
              @change="() => select_execute_time.onFieldChange()" />
            <template v-if="planBody.task_type === 5">
              <div>
                <span style="color: white;">-</span>
              </div>
              <a-time-picker v-model:value="planBody.select_time[n - 1][1]" format="HH:mm:ss" show-time
                placeholder="结束时间" style="width: 40%;" />
            </template>
            <div class="time-picker-btn">
              <PlusCircleOutlined class="mr5" style="color: #1890ff;" @click="addTime" />
              <MinusCircleOutlined @click="removeTime"
                :style="planBody.select_time_number === 1 ? 'color: gray' : 'color: red;'" />
            </div>
          </div>
        </a-form-item>

        <template v-if="planBody.task_type === 5">
          <!-- battery capacity -->
          <a-form-item label="任务开始执行的电量" :labelCol="{ span: 23 }" name="min_battery_capacity">
            <a-input-number class="width-100" v-model:value="planBody.min_battery_capacity" :min="50" :max="100"
              :formatter="(value: number) => `${value}%`" :parser="(value: string) => value.replace('%', '')">
            </a-input-number>
          </a-form-item>
        </template>

        <!-- Repeat Rules -->
        <template v-if="[2, 5].includes(planBody.task_type)">
          <!-- 重复频率 -->
          <a-form-item label="重复频率">
            <div class="repeat-frequency-wrapper">
              <span class="prefix-text">每</span>
              <a-input-number v-model:value="planBody.repeat_option.interval" :min="1" />
              <a-select v-model:value="planBody.repeat_frequency_unit" @change="onRepeatFrequencyUnitChange"
                style="width: 100px;">
                <a-select-option v-for="opt in repeatFrequencyUnitOptions" :key="opt.value" :value="opt.value">{{
                  opt.label
                }}</a-select-option>
              </a-select>
            </div>
          </a-form-item>

          <!-- 重复规则 for weekly -->
          <template v-if="planBody.repeat_frequency_unit === 'weekly'">
            <a-form-item>
              <div class="day-selector-wrapper">
                <button v-for="day in weekDays" :key="day.value" class="day-selector-item"
                  :class="{ selected: planBody.repeat_option.days_of_week.includes(day.value) }"
                  @click="toggleDayOfWeek(day.value)">
                  {{ day.label }}
                </button>
              </div>
            </a-form-item>
          </template>

          <!-- 重复规则 for monthly -->
          <template v-if="planBody.repeat_frequency_unit === 'monthly'">
            <a-form-item label="重复规则">
              <a-select v-model:value="planBody.repeat_type" style="width: 100%">
                <a-select-option v-for="opt in monthRepeatRuleOptions" :key="opt.value" :value="opt.value">{{ opt.label
                }}</a-select-option>
              </a-select>
            </a-form-item>

            <!-- by date -->
            <template v-if="planBody.repeat_type === 6">
              <a-form-item>
                <div class="day-selector-wrapper">
                  <button v-for="day in 31" :key="day" class="day-selector-item"
                    :class="{ selected: planBody.repeat_option.days_of_month.includes(day) }"
                    @click="toggleDayOfMonth(day)">
                    {{ day }}
                  </button>
                </div>
              </a-form-item>
            </template>

            <!-- by week -->
            <template v-if="planBody.repeat_type === 7">
              <a-form-item>
                <div class="repeat-frequency-wrapper" style="gap: 10px;">
                  <a-select v-model:value="planBody.repeat_option.week_of_month" style="flex: 1">
                    <a-select-option v-for="opt in weekOfMonthOptions" :key="opt.value" :value="opt.value">{{ opt.label
                    }}</a-select-option>
                  </a-select>
                  <a-select v-model:value="planBody.repeat_option.days_of_week[0]" style="flex: 1">
                    <a-select-option v-for="day in weekDays" :key="day.value" :value="day.value">{{ day.label
                    }}</a-select-option>
                  </a-select>
                </div>
              </a-form-item>
            </template>
          </template>
        </template>

        <!-- 智能规划最佳返航路线 -->
        <a-form-item>
          <template #label>
            智能规划最佳返航路线
            <a-tooltip title="开启后，飞行器将根据环境因素（如障碍物、GEO等）智能规划一条更安全、高效的返航路径。关闭则使用预设返航路线。">
              <QuestionCircleOutlined class="ml5" />
            </a-tooltip>
          </template>
          <a-switch v-model:checked="planBody.rth_mode_optimal" />
        </a-form-item>
        <!-- RTH Altitude Relative to Dock -->
        <a-form-item name="rth_altitude">
          <template #label>
            返航高度（相对机场返航高度）
            <a-tooltip title="任务中飞行器返航时，将先爬升至此高度，再执行返航动作。">
              <QuestionCircleOutlined class="ml5" />
            </a-tooltip>
          </template>
          <div class="rth-altitude-wrapper">
            <div class="rth-btns">
              <a-button size="small" @click="adjustRthAltitude(-100)">-100</a-button>
              <a-button size="small" @click="adjustRthAltitude(-10)">-10</a-button>
              <!-- <a-button size="small" @click="adjustRthAltitude(-1)">-1</a-button> -->
            </div>
            <a-input-number v-model:value="planBody.rth_altitude" :min="20" :max="1500" style="flex: 1;" />
            <span class="unit">m</span>
            <div class="rth-btns">
              <!-- <a-button size="small" @click="adjustRthAltitude(1)">+1</a-button> -->
              <a-button size="small" @click="adjustRthAltitude(10)">+10</a-button>
              <a-button size="small" @click="adjustRthAltitude(100)">+100</a-button>
            </div>
          </div>
        </a-form-item>
        <!-- Lost Action -->
        <a-form-item name="out_of_control_action_in_flight">
          <template #label>
            航线飞行中失联
            <a-tooltip title="任务过程中，当飞行器与遥控器信号断开时，飞行器执行的动作。">
              <QuestionCircleOutlined class="ml5" />
            </a-tooltip>
          </template>
          <div style="white-space: nowrap;">
            <a-radio-group v-model:value="planBody.out_of_control_action_in_flight" button-style="solid" size="small">
              <a-radio-button v-for="action in OutOfControlActionOptionsNew" :value="action.value" :key="action.value">
                {{ action.label }}
              </a-radio-button>
            </a-radio-group>
          </div>
        </a-form-item>

        <!-- 完成动作 -->
        <a-form-item name="completion_action">
          <template #label>
            完成动作
            <a-tooltip title="任务完成后，飞行器将执行的动作">
              <QuestionCircleOutlined class="ml5" />
            </a-tooltip>
          </template>
          <a-select v-model:value="planBody.completion_action" placeholder="请选择" style="width: 100%;">
            <a-select-option value="return_home">自动返航</a-select-option>
            <a-select-option value="hover" disabled>悬停</a-select-option>
            <a-select-option value="land" disabled>原地降落</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 自动断点续飞 -->
        <a-form-item>
          <template #label>
            自动断点续飞
            <a-tooltip title="开启后，任务意外中断，飞行器将在安全状态下自动返回断点处继续执行任务。">
              <QuestionCircleOutlined class="ml5" />
            </a-tooltip>
          </template>
          <a-switch v-model:checked="planBody.resumable_status_auto" />
        </a-form-item>

        <a-form-item class="width-100">
          <div class="footer" style="position: absolute;bottom: 0;width: 100%;">
            <!--<a-button class="mr10" style="background: #3c3c3c; width: 45%" @click="closePlan">-->
            <!--  取消-->
            <!--</a-button>-->
            <a-button type="primary" style="width: 100%;" @click="onSubmit" :disabled="disabled">
              确定
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </div>

    <el-dialog v-model="isAdd" title="航线" width="900" :close-on-click-modal="false" style="height: 600px;">
      <Airline @onSelectedRowChanged="handleSelectedRowChange"></Airline>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { computed, reactive, ref, watch } from 'vue';
import { PlusCircleOutlined, MinusCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';
import { ERouterName } from '@/api/enum/index';
import store from '@/store';
import { WaylineFile } from '@/types/wayline';
import { Device, DEVICE_NAME } from '@/types/device';
import { createPlan, CreatePlan } from '@/api/wayline';
import { getRoot } from '@/root';
import { TaskType, OutOfControlActionOptions, OutOfControlAction, TaskTypeOptions } from '@/types/task';
import dayjs, { Dayjs } from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
dayjs.extend(isSameOrBefore)
import { RuleObject } from 'ant-design-vue/es/form/interface';
import { message } from 'ant-design-vue';
import router from '@/router';
import { getWaylineFiles } from '@/api/wayline';
import { getProjectUuid } from '@/utils/storage';
import { getWayLineList } from '@/api/workspace/index';
import { getBindingDevices } from '@/api/manage';
import { getDeviceListByProjectUuid } from '@/api/workspace/index'
import { EDeviceTypeName, ELocalStorageKey } from '@/types';
import EventBus from '@/event-bus/';
import Airline from '@/views/task/airline.vue';
import { getWorkspaceId } from '@/utils/storage'

const root = getRoot();
const wayline = computed<WaylineFile>(() => {
  return store.state.dock.waylineInfo;
});
const dock = computed<Device>(() => {
  return store.state.dock.dockInfo;
});
const disabled = ref(false);
const formState = reactive({
  layout: 'vertical',
});

const repeatFrequencyUnitOptions = [
  { label: '天', value: 'daily' },
  { label: '周', value: 'weekly' },
  { label: '月', value: 'monthly' },
];

const weekDays = [
  { label: '周日', value: 0 },
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
];

const monthRepeatRuleOptions = [
  { label: '按日期', value: 6 }, // 每几月（按日期）
  { label: '按星期', value: 7 }, // 每几月(按星期)
]

const weekOfMonthOptions = [
  { label: '第一个', value: 1 },
  { label: '第二个', value: 2 },
  { label: '第三个', value: 3 },
  { label: '第四个', value: 4 },
];

const TaskStrategyOptions = [
  { label: '立即', value: 1 },
  { label: '单次定时', value: 3 },
  { label: '重复定时', value: 2 },
  { label: '连续执行', value: 5 },
];

const OutOfControlActionOptionsNew = [
  { label: '返航', value: 1 },
  { label: '继续执行', value: 2 },
]

const planBody = reactive({
  name: '',
  wayline_uuid: '',
  wayline_duration: 0,
  default_name: '',
  // wayline_uuid: computed(() => store.state.dock?.waylineInfo.id),
  sn: '',

  mission_type: 'normal', // 任务类型: 普通任务 normal, 蛙跳任务 leapfrog
  landing_dock_sn: '', // 降落机场
  wayline_precision_type: 1, // 任务精度: 0-GPS，1-RTK
  task_type: 1, // 任务类型: 1 立即任务, 2 重复任务, 3 单次定时任务, 5 连续任务
  rth_mode_optimal: false, // 智能规划最佳返航路线
  completion_action: 'return_home', // 完成动作
  resumable_status_auto: false, // 自动断点续飞

  select_execute_date: dayjs() as Dayjs,
  select_execute_date_range: [dayjs(), dayjs()] as Dayjs[],
  select_time_number: 1,
  select_time: [[]] as Dayjs[][],
  rth_altitude: 100,
  out_of_control_action_in_flight: 1, // 航线失控动作：1-返航 2-继续执行
  min_battery_capacity: 90 as number,

  // 新的重复类型参数
  repeat_type: 4, // 任务重复类型: 0-不重复, 1-每几秒, 2-每几分钟, 3-每几小时, 4-每几天, 5-每几周, 6-每几月（按日期）, 7-每几月(按星期), 8-绝对每年
  repeat_frequency_unit: 'daily',
  repeat_option: {
    interval: 1,
    days_of_week: [] as number[], // for weekly and relative_monthly
    days_of_month: [] as number[], // for absolute_monthly
    week_of_month: 1, // for relative_monthly
  },

  // 新增字段
  business_type: undefined as number | undefined, // 任务的业务类型，执行任务的航线如果是AI航线，需要设置此字段值为1
  // cloud_to_cloud_id: undefined as string | undefined, // 云云对接id
  dow: [] as number[], // 一周中的第几天
  // push_c2c_organization_uuid: undefined as string | undefined, // 云云对接推送到组织的uuid
  // subscription_alert_cloud_to_cloud_frequency: undefined as number | undefined, // 云云对接告警频率
  // subscription_alert_email_frequency: undefined as number | undefined, // 邮件告警频率
  // subscription_alert_phone_frequency: undefined as number | undefined, // 短信告警频率
  // subscription_alert_user_web_frequency: undefined as number | undefined, // 用户站内订阅频率
  //subscription_receive_type: [] as string[], // 订阅告警通知类型
  // subscription_user_id: [] as string[], // 订阅用户ID列表
  // tags: [] as string[], // 云云对接标签
  wom: undefined as number | undefined, // 一个月中的第几周
});
const valueRef = ref();
const select_execute_time = ref<any>();
const rules = {
  name: [
    { required: true, message: '请输入计划名称' },
    { max: 20, message: '长度应为1到20' },
  ],
  wayline_uuid: [{ required: true, message: '请选择航线' }],
  sn: [{ required: true, message: '请选择设备' }],
  select_execute_time: [
    {
      validator: async (rule: RuleObject, value: Dayjs[]) => {
        validEndTime();
        validStartTime();
        if (planBody.select_time.length < planBody.select_time_number) {
          throw new Error('请选择时间');
        }
        validOverlapped();
      },
    },
  ],
  select_execute_date: [{ required: true, message: '请选择执行日期' }],
  select_execute_date_range: [{ required: true, message: '请选择执行日期' }],
  rth_altitude: [
    {
      validator: async (rule: RuleObject, value: string) => {
        if (!/^[0-9]{1,}$/.test(value)) {
          throw new Error('请设置相对机场返航高度');
        }
      },
    },
  ],
  min_battery_capacity: [
    {
      validator: async (rule: RuleObject, value: any) => {
        if (5 === planBody.task_type && !value) { // 连续任务
          throw new Error('请输入电池容量');
        }
      },
    },
  ],
  out_of_control_action_in_flight: [{ required: true, message: '请选择失联动作' }],
};
const pagination = {
  page: 1,
  total: -1,
  page_size: 10,
};
const waylinesData = reactive({
  data: [] as WaylineFile[],
});

const disabledDate = (current: Dayjs) => {
  return current && current < dayjs().startOf('day');
};

//获取航线
function getWaylines() {
  getWayLineList({
    projectUuid: getProjectUuid()
  }).then(res => {
    if (res.data.code !== 200) {
      return;
    }
    waylinesData.data = [...waylinesData.data, ...res.data.data];
    // pagination.total = res.data.data.pagination.total;
    // pagination.page = res.data.data.pagination.page;
  })
}

watch(() => planBody.mission_type, (newVal) => {
  if (newVal === 'leapfrog') {
    planBody.wayline_precision_type = 1; // RTK
  }
})

// 监听任务类型变化，重置重复类型设置
watch(() => planBody.task_type, (newVal) => {
  if (newVal === 1) { // 立即任务
    planBody.repeat_type = 0; // 不重复
  } else if ([2, 5].includes(newVal)) { // 重复任务或连续任务
    // 根据当前的重复频率单位设置对应的重复类型
    if (planBody.repeat_frequency_unit === 'daily') {
      planBody.repeat_type = 4; // 每几天
    } else if (planBody.repeat_frequency_unit === 'weekly') {
      planBody.repeat_type = 5; // 每几周
    } else if (planBody.repeat_frequency_unit === 'monthly') {
      planBody.repeat_type = 6; // 每几月（按日期）- default
    }
  } else if (newVal === 3) { // 单次定时任务
    planBody.repeat_type = 0; // 不重复
  }
})

const body = {
  page: 1,
  total: -1,
  page_size: 10,
}

const docksData = reactive({
  data: [] as Device[]
})

//获取设备
async function getDocks() {
  await getDeviceListByProjectUuid({
    projectUuid: getProjectUuid()
  }).then(res => {
    if (res.data.code !== 200) {
      return
    }
    docksData.data = res.data.data.map(item => ({
      device_sn: item.gateway.sn,
      nickname: item.gateway.callsign
    }))
  })
}

getWaylines();
getDocks();

// 初始化设备值
if (store.state.dock?.dockInfo?.device_sn) {
  planBody.sn = store.state.dock.dockInfo.device_sn;
}

function validStartTime() {
  for (let i = 0; i < planBody.select_time.length; i++) {
    if (!planBody.select_time[i][0]) {
      throw new Error('请选择开始时间');
    }
  }
}

function validEndTime(): Error | void {
  if (5 !== planBody.task_type) return; // 连续任务
  for (let i = 0; i < planBody.select_time.length; i++) {
    if (!planBody.select_time[i][1]) {
      throw new Error('请选择结束时间');
    }
    if (
      planBody.select_time[i][0] &&
      planBody.select_time[i][1].isSameOrBefore(planBody.select_time[i][0])
    ) {
      throw new Error('结束时间应晚于开始时间');
    }
  }
}

function validOverlapped(): Error | void {
  if (planBody.task_type !== 5) return; // 连续任务
  const arr = planBody.select_time.slice();
  arr.sort((a, b) => a[0].unix() - b[0].unix());
  arr.forEach((v, i, arr) => {
    if (i > 0 && v[0].unix() < arr[i - 1][1].unix()) {
      throw new Error('重叠时间段.');
    }
  });
}

function onSubmit() {
  valueRef.value.validate().then(() => {
    disabled.value = true;
    const createPlanBody = { ...planBody } as any;

    // 根据任务类型设置不同的参数
    if (planBody.task_type === 3) { // 单次定时任务
      if (planBody.select_execute_date.isBefore(dayjs())) {
        message.error('执行日期不能小于当前时间');
        disabled.value = false;
        return;
      }

      // 单次定时任务参数
      createPlanBody.begin_at = planBody.select_execute_date.unix();
    } else if ([2, 5].includes(planBody.task_type)) { // 重复任务或连续任务
      if (planBody.select_execute_date_range?.length === 2) {
        // 设置开始和结束时间
        createPlanBody.begin_at = planBody.select_execute_date_range[0].unix();
        createPlanBody.end_at = planBody.select_execute_date_range[1].unix();
        createPlanBody.interval = planBody.repeat_option.interval;
        createPlanBody.repeat_type = planBody.repeat_type;

        // 根据重复类型设置不同的参数
        if (planBody.repeat_frequency_unit === 'daily') {
          // 每天重复任务，不需要设置 dow 或 dom 参数
          // repeat_type = 4 (每几天) 已经在 onRepeatFrequencyUnitChange 中设置
        } else if (planBody.repeat_frequency_unit === 'weekly') {
          createPlanBody.dow = planBody.repeat_option.days_of_week;
        } else if (planBody.repeat_frequency_unit === 'monthly') {
          if (planBody.repeat_type === 6) { // 按日期
            createPlanBody.dom = planBody.repeat_option.days_of_month;
          } else if (planBody.repeat_type === 7) { // 按星期
            createPlanBody.dow = [planBody.repeat_option.days_of_week[0]];
            createPlanBody.wom = planBody.repeat_option.week_of_month;
          }
        }

        if (planBody.task_type === 2) { // 重复任务
          // 转换时间格式为数字数组
          createPlanBody.extended_begin_at = [];
          for (let i = 0; i < planBody.select_time.length; i++) {
            // createPlanBody.extended_begin_at.push(planBody.select_time[i][0].unix());
            createPlanBody.extended_begin_at.push(createPlanBody.begin_at);
            createPlanBody.end_at = planBody.select_execute_date_range[1].endOf('day').unix();
          }
        } else if (planBody.task_type === 5) { // 连续任务
          // 转换时间段格式为字符串数组
          createPlanBody.continuous_task_periods = [];
          for (let i = 0; i < planBody.select_time.length; i++) {
            createPlanBody.continuous_task_periods.push(
              `${planBody.select_time[i][0].unix()},${planBody.select_time[i][1].unix()}`
            );
          }
          createPlanBody.min_battery_capacity = planBody.min_battery_capacity;
        }
      }
    }

    // 设置通用参数
    createPlanBody.rth_altitude = Number(createPlanBody.rth_altitude);
    createPlanBody.rth_mode = planBody.rth_mode_optimal ? 0 : 1; // 0-智能高度，1-设定高度
    createPlanBody.resumable_status = planBody.resumable_status_auto ? 'auto' : 'manual';

    // 确保 task_type 是数字而不是对象
    createPlanBody.task_type = planBody.task_type;

    // 根据重复类型清理不必要的参数
    if ([2, 5].includes(planBody.task_type)) { // 重复任务或连续任务
      if (planBody.repeat_frequency_unit === 'daily') {
        // 每天重复任务，不需要 dow 和 dom 参数
        delete createPlanBody.dow;
        delete createPlanBody.dom;
        delete createPlanBody.wom;
      } else if (planBody.repeat_frequency_unit === 'weekly') {
        // 每周重复任务，不需要 dom 和 wom 参数
        delete createPlanBody.dom;
        delete createPlanBody.wom;
      } else if (planBody.repeat_frequency_unit === 'monthly') {
        if (planBody.repeat_type === 6) { // 按日期
          // 按日期重复，不需要 dow 和 wom 参数
          delete createPlanBody.dow;
          delete createPlanBody.wom;
        } else if (planBody.repeat_type === 7) { // 按星期
          // 按星期重复，不需要 dom 参数
          delete createPlanBody.dom;
        }
      }
    } else {
      // 非重复任务，清理重复相关参数
      delete createPlanBody.dow;
      delete createPlanBody.dom;
      delete createPlanBody.wom;
      delete createPlanBody.interval;
      delete createPlanBody.end_at;
      delete createPlanBody.extended_begin_at;
      delete createPlanBody.continuous_task_periods;

      // 立即任务不需要 repeat_type 和 begin_at
      if (planBody.task_type === 1) {
        delete createPlanBody.repeat_type;
        delete createPlanBody.begin_at;
      } else if (planBody.task_type === 3) {
        // 单次定时任务需要 begin_at，但不需要 repeat_type
        delete createPlanBody.repeat_type;
      }
    }

    // Cleanup properties that are for UI or not part of the final model for this task type
    delete createPlanBody.rth_mode_optimal;
    delete createPlanBody.resumable_status_auto;
    delete createPlanBody.repeat_frequency_unit;
    delete createPlanBody.repeat_option;
    delete createPlanBody.mission_type;
    delete createPlanBody.select_execute_date;
    delete createPlanBody.select_execute_date_range;
    delete createPlanBody.select_time;
    delete createPlanBody.select_time_number;

    createPlan(createPlanBody).then(res => {
      setNull();
      message.success(res.data.msg);
    }).finally(() => {
      disabled.value = false;
    });
  }).catch((e: any) => {
    console.log(e)
    disabled.value = false;
  });
}
/**
 * 关闭任务
 */
function setNull() {
  fileName.value = ''
  planBody.name = ''
  planBody.rth_altitude = 100
  planBody.task_type = 1 // 立即任务
  planBody.wayline_uuid = ''
  planBody.min_battery_capacity = 90
  planBody.select_time = [[]]
  planBody.select_execute_date = dayjs()
  planBody.select_execute_date_range = [dayjs(), dayjs()]
  planBody.select_time_number = 1
  planBody.repeat_type = 0 // 不重复
  planBody.repeat_frequency_unit = 'daily'
  planBody.repeat_option = {
    interval: 1,
    days_of_week: [],
    days_of_month: [],
    week_of_month: 1,
  }
  EventBus.emit('updateplan');
}

function closePlan() {
  router.push('/' + ERouterName.TASK);
}

function handleSelectChange(id) {
  store.state.dock.waylineInfo.id = id;
}

function dockChange(id) {
  store.state.dock.dockInfo.device_sn = id;
  planBody.sn = id; // 同时更新本地变量
}

function addTime() {
  valueRef.value.validateFields(['select_execute_time']).then(() => {
    planBody.select_time_number++;
    planBody.select_time.push([]);
  });
}

function removeTime() {
  if (planBody.select_time_number === 1) return;
  planBody.select_time_number--;
  planBody.select_time.splice(planBody.select_time_number);
}

//接收值
const fileName = ref('');
const isAdd = ref(false);
const canOpenDialog = ref(false); // 控制是否可以打开弹窗
const openDialog = () => {
  if (!canOpenDialog.value) {
    isAdd.value = true;
    // canOpenDialog.value = false; // 标记已选择
  }
};
const handleSelectedRowChange = (wayline: WaylineFile & { ext?: { duration?: number } }) => {
  planBody.wayline_uuid = wayline.id;
  planBody.wayline_duration = wayline?.ext?.duration || 0;
  planBody.default_name = wayline?.name;
  fileName.value = wayline.name;
  isAdd.value = false;
  canOpenDialog.value = true; // 标记已选择
  setTimeout(function () {
    canOpenDialog.value = false
  }, 100);
};

function adjustRthAltitude(val: number) {
  if (planBody.rth_altitude + val >= 20 && planBody.rth_altitude + val <= 1500) {
    planBody.rth_altitude += val;
  }
}

function onRepeatFrequencyUnitChange(value: string) {
  planBody.repeat_option.interval = 1;
  planBody.repeat_option.days_of_week = [];
  planBody.repeat_option.days_of_month = [];
  planBody.repeat_option.week_of_month = 1;

  if (value === 'daily') {
    planBody.repeat_type = 4; // 每几天
  } else if (value === 'weekly') {
    planBody.repeat_type = 5; // 每几周
  } else if (value === 'monthly') {
    planBody.repeat_type = 6; // 每几月（按日期）- default
  }
}

function toggleDayOfWeek(day: number) {
  const index = planBody.repeat_option.days_of_week.indexOf(day);
  if (index > -1) {
    planBody.repeat_option.days_of_week.splice(index, 1);
  } else {
    planBody.repeat_option.days_of_week.push(day);
  }
}

function toggleDayOfMonth(day: number) {
  const index = planBody.repeat_option.days_of_month.indexOf(day);
  if (index > -1) {
    planBody.repeat_option.days_of_month.splice(index, 1);
  } else {
    planBody.repeat_option.days_of_month.push(day);
  }
}
</script>

<style lang="scss" scoped>
:deep(.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  background-color: #232323;
  color: #fff;
}

:deep(.ant-picker) {
  background-color: #232323 !important;
}

:deep(.ant-picker-suffix) {
  color: #fff;
}

.rth-altitude-wrapper {
  display: flex;
  align-items: center;
  gap: 5px;

  .rth-btns {
    display: flex;
    gap: 5px;

    button {
      background-color: #383838;
      border-color: #585858;
      color: #fff
    }
  }

  .unit {
    // margin: 0 5px;
    color: white
  }

  :deep(.ant-input-number) {
    flex: 1;
    background-color: #232323;
    color: #fff
  }
}

.ml5 {
  margin-left: 5px;
}

.time-picker-wrapper {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 10px;
}

.time-picker-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  margin-left: 5px;
  font-size: 18px;
  display: inline-flex;
  height: 32px;
}

.repeat-frequency-wrapper {
  display: flex;
  align-items: center;
  gap: 5px;

  .prefix-text {
    color: white;
  }

  :deep(.ant-input-number) {
    background-color: #232323;
    color: #fff;
    border-color: #585858;
  }
}

.day-selector-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .day-selector-item {
    background-color: #383838;
    border: 1px solid #585858;
    color: #fff;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 4px;
    min-width: 40px;
    text-align: center;
    line-height: 22px;


    &.selected {
      background-color: #1890ff;
      border-color: #1890ff;
    }
  }
}
</style>

<style lang="scss">
.create-plan-wrapper {
  position: relative;
  // background-color: #232323;
  color: #fff;
  padding-bottom: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .header {
    height: 52px;
    border-bottom: 1px solid #4f4f4f;
    font-weight: 700;
    font-size: 16px;
    padding-left: 10px;
    display: flex;
    align-items: center;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  .content {
    height: calc(100% - 54px);
    overflow-y: auto;
    padding-bottom: 30px;

    .ant-input::placeholder,
    .ant-select-selection-placeholder,
    .ant-calendar-picker-input::placeholder,
    .ant-time-picker-input::placeholder,
    .ant-input-number-input::placeholder {
      color: #bfbfbf;
    }

    form {
      margin: 10px;
    }

    form label,
    input,
    .ant-input,
    .ant-calendar-range-picker-separator,
    .ant-input:hover,
    .ant-time-picker .anticon,
    .ant-calendar-picker .anticon {
      background-color: #232323;
      color: #fff;
    }

    .ant-input-suffix {
      color: #fff;
    }

    .plan-timer-form-item {
      .ant-radio-button-wrapper {
        background-color: #383838;
        color: #fff;
        border-color: #585858;
        flex: 1;
        text-align: center;

        &.ant-radio-button-wrapper-checked {
          background-color: #1890ff;
          border-color: #1890ff;
        }
      }
    }
  }

  .footer {
    display: flex;
    padding: 10px 0;

    button {
      width: 45%;
      color: #fff;
      border: 0;
    }
  }
}

.wayline-panel {
  background: #1f2b38;
  margin-left: auto;
  margin-right: auto;
  margin-top: 10px;
  height: 90px;
  width: 95%;
  font-size: 13px;
  border-radius: 2px;
  cursor: pointer;

  .title {
    display: flex;
    color: white;
    flex-direction: row;
    align-items: center;
    height: 30px;
    font-weight: bold;
    margin: 0 10px 0 10px;
  }
}

.panel {
  background: #192a3b;
  margin-left: auto;
  margin-right: auto;
  margin-top: 10px;
  height: 70px;
  width: 95%;
  font-size: 13px;
  border-radius: 2px;
  cursor: pointer;

  .title {
    display: flex;
    color: white;
    flex-direction: row;
    align-items: center;
    height: 30px;
    font-weight: bold;
    margin: 0 10px 0 10px;
  }
}

.cor-096 {
  color: #096dd9;
}
</style>